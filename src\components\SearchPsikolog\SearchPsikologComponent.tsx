'use client'

import { useEffect, useState } from 'react'
import { PsychologistCardItem } from '../home/<USER>/OurPsychologist/PsychologistCardItem'
import { MobileCardItem } from './section/MobileCardItem'
import { psychologistService } from '@/services/psychologist.service'
import { useDispatch } from '@/store'
import { useSearchParams } from 'next/navigation'
import { LoadingCard, LoadingCardWithAvatar } from '../loading/LoadingCard'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Dialog } from '../ui/dialog'
import { FilterExAndSpecPopup } from './filter/FilterExAndSpecPopup'
import { DatePicker } from '../ui/DatePicker'
import { AppSelect } from '../_common/Select/AppSelect'
import AppInput from '../_common/input/Input'
import ButtonPrimary from '../_common/ButtonPrimary'
import { IIcons, SVGIcons } from '../_common/icon'

const WAKTU_OPTIONS = ['MORNING', 'AFTERNOON', 'EVENING']

export const SearchPsikologComponent = () => {
  const searchparam = useSearchParams()
  const filter = searchparam.get('filter') || ''
  const dispatch = useDispatch()

  const [psikologList, setPsikologList] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isFilterExAndSpecOpen, setIsFilterExAndSpecOpen] = useState(false)

  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [selectedTime, setSelectedTime] = useState<string>('')
  const [searchQuery, setSearchQuery] = useState('')
  const [specializations, setSpecializations] = useState<string[]>([])
  const [experience, setExperience] = useState<string[]>([])

  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null)

  // Debounced Fetch
  useEffect(() => {
    if (filter) {
      getPsychologistByCategory(filter)
      return
    }

    if (debounceTimer) clearTimeout(debounceTimer)

    const timer = setTimeout(() => {
      getFilteredPsychologists()
    }, 500) // 500ms debounce

    setDebounceTimer(timer)
    return () => clearTimeout(timer)
  }, [searchQuery, selectedDate, selectedTime, specializations, experience])

  const getPsychologistByCategory = async (category: string) => {
    try {
      setIsLoading(true)
      const response = await psychologistService.getPsychologistByCategory(category)
      setPsikologList(response.data || [])
    } catch (error) {
      console.error(error)
    } finally {
      setIsLoading(false)
    }
  }

  const getFilteredPsychologists = async () => {
    try {
      setIsLoading(true)

      const dateString = selectedDate ? selectedDate.toISOString() : ''
      const times = selectedTime === 'ALL' ? ['MORNING', 'AFTERNOON', 'EVENING'] : [selectedTime]

      const response = await psychologistService.getPsychologistFiltered(
        specializations,
        experience,
        dateString,
        times,
        searchQuery
      )

      setPsikologList(response.data || [])
    } catch (error) {
      console.error('Error fetching filtered psychologists:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleApplyFilter = (specializations: string[], experience: string[]) => {
    setSpecializations(specializations)
    setExperience(experience)
  }

  const handleDateChange = (date: Date | null) => {
    setSelectedDate(date)
  }

  const handleTimeChange = (value: string) => {
    const timeValue = value === 'ALL' ? 'ALL' : value
    setSelectedTime(timeValue)
  }

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value)
  }

  return (
    <div className="relative -mt-5 md:-mt-16 gap-y-6 md:gap-y-[40px] max-w-MHmax w-full flex flex-col justify-center items-center xl:px-40 z-10">
      {/* Popup Filter */}
      <FilterExAndSpecPopup
        open={isFilterExAndSpecOpen}
        onClose={() => setIsFilterExAndSpecOpen(false)}
        onApplyFilter={handleApplyFilter as any}
      />
      {/* Dekorasi Gambar */}
      <div className="pointer-events-none absolute -top-[100px] md:-top-[200px] right-10 h-[100px] w-[100px] md:h-[200px] md:w-[200px] 2lg:right-12 xl:right-44 z-0">
        <Image src={'/ilustration/call.svg'} fill alt="call" />
      </div>

      {/* Konten Utama dengan Background Putih */}
      <div className="min-h-[500px] flex justify-center items-start relative w-full">
        <div className="w-full bg-white rounded-t-[24px] pt-6">
          {/* Filter Section - Di dalam background putih */}
          <div className="mb-6 px-4">
            {/* Mobile Layout - Stack vertically */}
            <div className="flex md:hidden flex-col w-full gap-3">
              {/* Search Bar - Full width on top for mobile */}
              <div className="w-full">
                <AppInput
                  prefixIcon={true}
                  type="text"
                  placeholder="Cari Psikolog"
                  value={searchQuery}
                  onChange={handleSearchChange}
                  inputClass="px-4 py-3 rounded-md w-full h-[48px]"
                />
              </div>

              {/* Filter Row - Hari, Waktu, Filter Button for mobile */}
              <div className="flex w-full gap-2">
                {/* Filter Hari */}
                <div className="flex-1">
                  <DatePicker
                    prefixIcon={false}
                    placeholder="Hari"
                    className="py-3 h-[48px] w-full shadow-none text-gray-200"
                    date={selectedDate as any}
                    onSelect={handleDateChange as any}
                    captionLayout="dropdown"
                    fromYear={1950}
                    toYear={new Date().getFullYear()}
                  />
                </div>

                {/* Filter Waktu */}
                <div className="flex-1">
                  <AppSelect
                    options={[
                      { label: 'Semua Waktu', value: 'ALL' },
                      { label: 'Pagi', value: 'MORNING' },
                      { label: 'Siang', value: 'AFTERNOON' },
                      { label: 'Sore', value: 'EVENING' },
                    ]}
                    placeholder="Waktu"
                    value={selectedTime}
                    onChange={(value) => handleTimeChange(value)}
                    className="w-full h-[48px] rounded-md text-gray-200 text-sm"
                  />
                </div>

                {/* Filter Pop-up Button - Icon only for mobile */}
                <div className="flex-shrink-0">
                  <Button
                    onClick={() => setIsFilterExAndSpecOpen(true)}
                    variant="outline"
                    className="h-[48px] w-[48px] shadow-none rounded-md p-0 flex items-center justify-center"
                  >
                    <SVGIcons name={IIcons.Filter} />
                  </Button>
                </div>
              </div>
            </div>

            {/* Desktop Layout - Keep original horizontal layout */}
            <div className="hidden md:flex justify-center w-full gap-2 md:gap-3 flex-wrap md:flex-nowrap">
              {/* Cari Psikolog - wider than others */}
              <div className="w-full md:w-2/5 lg:w-1/2">
                <AppInput
                  type="text"
                  placeholder="Cari Psikolog..."
                  value={searchQuery}
                  onChange={handleSearchChange}
                  inputClass="px-4 py-2 rounded-md border-line-200 shadow-none w-full h-[50px]"
                />
              </div>

              {/* Filter Hari */}
              <div className="w-full sm:w-1/3 md:w-1/5 lg:w-1/6">
                <DatePicker
                  placeholder="Hari"
                  className="py-3 h-[50px] w-full border-line-200 shadow-none text-gray-200"
                  date={selectedDate as any}
                  onSelect={handleDateChange as any}
                  captionLayout="dropdown"
                  fromYear={1950}
                  toYear={new Date().getFullYear()}
                />
              </div>

              {/* Filter Waktu */}
              <div className="w-full sm:w-1/3 md:w-1/5 lg:w-1/6">
                <AppSelect
                  options={[
                    { label: 'Semua Waktu', value: 'ALL' },
                    { label: 'Pagi', value: 'MORNING' },
                    { label: 'Siang', value: 'AFTERNOON' },
                    { label: 'Sore', value: 'EVENING' },
                  ]}
                  placeholder="Waktu"
                  value={selectedTime}
                  onChange={(value) => handleTimeChange(value)}
                  className="w-full bg-white h-[50px] rounded-md text-gray-200"
                />
              </div>

              {/* Filter Pop-up */}
              <div className="w-full sm:w-1/3 md:w-1/5 lg:w-1/6">
                <Button
                  onClick={() => setIsFilterExAndSpecOpen(true)}
                  variant="outline"
                  className="px-4 md:px-6 h-[50px] w-full border-line-200 shadow-none"
                >
                  <span className="flex items-center justify-center md:justify-start w-full">
                    <SVGIcons name={IIcons.Filter} className="mr-2 md:mr-4" />
                    <span className="text-gray-500 font-light text-sm md:text-base">Filter</span>
                  </span>
                </Button>
              </div>
            </div>
          </div>

          {/* Content Section */}
          {isLoading ? (
            <div className="hidden absolute 2lg:flex gap-4 w-full top-23 px-4">
              <LoadingCard />
              <LoadingCard />
              <LoadingCard />
            </div>
          ) : (
            <div className="hidden 2lg:grid 2lg:grid-cols-3 w-full gap-6 px-4">
              {psikologList.map((item) => (
                <PsychologistCardItem key={item.id} {...item} />
              ))}
            </div>
          )}

          {/* Tampilan Mobile */}
          <div className="2lg:hidden grid grid-cols-1 gap-y-4 w-full px-4">
            {isLoading ? (
              <>
                <LoadingCardWithAvatar />
                <LoadingCardWithAvatar />
                <LoadingCardWithAvatar />
              </>
            ) : (
              psikologList.map((item) => <MobileCardItem key={item.id} {...item} />)
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
