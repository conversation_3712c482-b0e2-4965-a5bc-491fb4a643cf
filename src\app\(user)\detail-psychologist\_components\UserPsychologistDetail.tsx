'use client'
import { IIcons, SVGIcons } from '@/components/_common/icon'
import { UserPsychologistDetailProps, useUserPsychologistDetails } from '../hook/useUserPsychologistDetail'
import { useGetPsychologistAvailabilityDate } from '../hook/useGetPsychologistAvailabilityDate'
import Image from 'next/image'
import PsychologistVideoCarousel from './section/VideoSection'
import { useSelector } from '@/store'
import { Button } from '@/components/ui/button'
import { H2 } from '@/components/_common/ui'
import BookingSection from './section/BookingSection'
import { useState } from 'react'
import BookingModal from './section/BookingModal'
import { QuoteIcon } from '@radix-ui/react-icons'
import { toast } from '@/components/ui/use-toast'

export default function UserPsychologistDetail({ psychologistId }: UserPsychologistDetailProps) {
  const { user } = useSelector((state) => state.Authentication)
  const [isBookingModalOpen, setIsBookingModalOpen] = useState(false)

  const { psychologist, loadingPsycho, errorPsycho } = useUserPsychologistDetails(psychologistId)
  const { availabilityDate, loadingDate, errorDate } = useGetPsychologistAvailabilityDate(psychologistId)

  // Handle share button click
  const handleShareClick = async () => {
    try {
      const profileUrl = `${window.location.origin}/detail-psychologist/${psychologistId}`
      await navigator.clipboard.writeText(profileUrl)

      toast({
        title: 'Berhasil',
        description: 'Link profil psikolog berhasil disalin',
        variant: 'success',
      })
    } catch (error) {
      console.error('Failed to copy link:', error)
      toast({
        title: 'Gagal',
        description: 'Gagal menyalin link profil',
        variant: 'danger',
      })
    }
  }

  // Extract problem categories from psychologist data
  const specializations = psychologist?.problemCategory?.map((item) => item.problemCategory) || []

  // Calculate years of experience
  const getExperienceYears = (): number => {
    if (!psychologist?.firstCareerDate) return 0

    const firstCareerDate = new Date(psychologist.firstCareerDate)
    const now = new Date()
    return Math.floor((now.getTime() - firstCareerDate.getTime()) / (1000 * 60 * 60 * 24 * 365))
  }

  if (loadingPsycho || loadingDate) {
    return (
      <div className="w-full max-w-4xl mx-auto p-4 flex justify-center items-center min-h-40">
        <div className="animate-pulse text-main-500">Loading...</div>
      </div>
    )
  }

  if (errorPsycho || !psychologist || errorDate) {
    return (
      <div className="w-full max-w-4xl mx-auto p-4 text-center text-red-500">
        Error loading psychologist data
      </div>
    )
  }

  return (
    <>
      <div className="flex flex-col w-full max-w-5xl mx-auto px-3 md:px-6 mt-4 sm:mt-6 pb-20 md:pb-8">
        {/* Profile Header Section */}
        <div className="relative w-full">
          <div className="relative pb-4">
            {/* Mobile/Desktop Photo Layout */}
            <div className="flex flex-col sm:flex-row sm:items-start">
              {/* Profile Photo - left aligned on all screens */}
              <div className="flex justify-start mb-4 sm:mb-0 sm:mr-6 sm:pl-4">
                <div className="relative w-28 h-28 sm:w-32 sm:h-32 rounded-full overflow-hidden">
                  {psychologist.profilePhoto ? (
                    <Image
                      width={128}
                      height={128}
                      src={psychologist.profilePhoto}
                      alt={psychologist.fullName}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-r from-main-100 to-purple-300"></div>
                  )}
                </div>
              </div>

              {/* Text Content - left aligned on all screens */}
              <div className="sm:flex-1 text-left">
                {/* Name */}
                <div className="mt-4 sm:mt-0">
                  <div className="flex items-center justify-start gap-2 flex-wrap">
                    <h1 className="text-xl sm:text-2xl font-bold break-words">{psychologist.fullName}</h1>
                  </div>
                  <p className="text-gray-400 mt-1 break-words">
                    {psychologist.field && psychologist.field[0]?.name
                      ? psychologist.field[0].name
                      : 'Psikolog Klinis'}
                  </p>
                </div>

                {/* Rating and experience */}
                <div className="flex items-center justify-start gap-3 mt-3 flex-wrap">
                  <div className="flex items-center bg-gray-50 px-2 py-1 rounded-full">
                    <svg
                      className="w-4 h-4 text-yellow-400 flex-shrink-0"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    <span className="ml-1 text-sm font-medium">{psychologist.overallRating || 5.0}</span>
                    <span className="text-gray-500 ml-1 text-sm">({psychologist.testimonyCount} ulasan)</span>
                  </div>
                  <div className="flex items-center bg-gray-50 px-2 py-1 rounded-full">
                    <SVGIcons name={IIcons.SuitCase} />
                    <span className="ml-1 text-sm font-medium">{getExperienceYears()} tahun</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Share button - remains at top right */}
            <div className="absolute top-4 right-4">
              <Button
                onClick={handleShareClick}
                className="w-10 h-10 rounded-full flex items-center justify-center p-0"
                variant="outline"
                size="icon"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 30 30"
                  className="text-gray-800"
                >
                  <path d="M 23 3 A 4 4 0 0 0 19 7 A 4 4 0 0 0 19.09375 7.8359375 L 10.011719 12.376953 A 4 4 0 0 0 7 11 A 4 4 0 0 0 3 15 A 4 4 0 0 0 7 19 A 4 4 0 0 0 10.013672 17.625 L 19.089844 22.164062 A 4 4 0 0 0 19 23 A 4 4 0 0 0 23 27 A 4 4 0 0 0 27 23 A 4 4 0 0 0 23 19 A 4 4 0 0 0 19.986328 20.375 L 10.910156 15.835938 A 4 4 0 0 0 11 15 A 4 4 0 0 0 10.90625 14.166016 L 19.988281 9.625 A 4 4 0 0 0 23 11 A 4 4 0 0 0 27 7 A 4 4 0 0 0 23 3 z"></path>
                </svg>
              </Button>
            </div>
          </div>
        </div>

        {/* Specialization Section - Mobile: after header, Desktop: hidden */}
        <div className="block lg:hidden mt-4">
          <h2 className="text-base sm:text-lg font-medium mb-2">Spesialisasi</h2>

          <div className="flex flex-wrap gap-2">
            {specializations.length > 0 ? (
              specializations.map((spec) => (
                <span key={spec} className="px-3 py-1.5 text-xs text-gray-700 border rounded-full">
                  {spec}
                </span>
              ))
            ) : (
              <p className="text-xs text-gray-500">Tidak ada spesialisasi yang ditampilkan</p>
            )}
          </div>
        </div>

        {/* Main Content with Responsive Ordering */}
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Desktop Booking Section */}
          <div className="hidden lg:block w-full lg:w-1/3 order-first lg:order-last mb-4 lg:mb-0">
            <div className="sticky top-4">
              <BookingSection user={user} psychologist={psychologist} availabilityDate={availabilityDate} />
            </div>
          </div>

          {/* Left Column Content - For desktop, makes up 2/3 of width */}
          <div className="w-full lg:w-2/3 order-last lg:order-first flex flex-col">
            {/* Video Section - Desktop only */}
            <div className="hidden lg:block w-full mb-4">
              <div className="relative w-full rounded-lg overflow-hidden">
                {/* Aspect ratio container */}
                <div className="w-full relative pb-[60%] sm:pb-[56.25%]">
                  <div className="absolute inset-0">
                    <PsychologistVideoCarousel
                      video={psychologist.video}
                      youtubeVideos={psychologist.youtubeVideo}
                      profilePhoto={psychologist.profilePhoto}
                    />
                  </div>
                </div>
              </div>
              <div className="flex items-center justify-between mt-2">
                <span className="text-sm text-gray-600">5 Minute Mindfulness Meditation</span>
                <a href="#" className="text-main-500 text-sm font-medium">
                  Lihat semua
                </a>
              </div>
            </div>

            {/* Specialization Section - Desktop only */}
            <div className="hidden lg:block mt-2 mb-4">
              <H2 className="text-base sm:text-lg font-medium mb-3">Spesialisasi</H2>
              <div className="flex flex-wrap gap-2">
                {specializations.length > 0 ? (
                  specializations.map((spec) => (
                    <span key={spec} className="px-3 py-1.5 text-xs text-gray-700 border rounded-full">
                      {spec}
                    </span>
                  ))
                ) : (
                  <p className="text-xs sm:text-sm text-gray-500">Tidak ada spesialisasi yang ditampilkan</p>
                )}
              </div>
            </div>

            {/* Education Section */}
            <div className="mt-4 sm:mt-6">
              <h2 className="text-base sm:text-lg font-medium mb-2">Pendidikan</h2>
              {psychologist.educationHistory && psychologist.educationHistory.length > 0 ? (
                psychologist.educationHistory.map((education, index) => (
                  <div key={index} className="flex items-start gap-2 sm:gap-3 mb-2 sm:mb-3">
                    <div className="p-1.5 sm:p-2 bg-main-100 rounded-lg flex-shrink-0">
                      <div className="text-main-500">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M22 10v6M2 10l10-5 10 5-10 5z" />
                          <path d="M6 12v5c0 2 2 3 6 3s6-1 6-3v-5" />
                        </svg>
                      </div>
                    </div>
                    <div className="min-w-0 flex-grow">
                      <h3 className="font-medium text-xs sm:text-sm truncate">{education.major}</h3>
                      <p className="text-xs text-gray-600 truncate">
                        {education.level} {education.university}
                      </p>
                      <p className="text-xs text-gray-500">
                        {education.graduationYear && formatEducationYear(education.graduationYear)}
                      </p>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-xs sm:text-sm text-gray-500">Tidak ada informasi pendidikan</p>
              )}
            </div>

            {/* About Section */}
            <div className="mt-4 sm:mt-6">
              <h2 className="text-base sm:text-lg font-medium mb-2">Tentang Psikolog</h2>
              <p className="text-xs sm:text-sm text-gray-700 break-words leading-relaxed">
                {psychologist.bio || 'Tidak ada informasi tentang psikolog ini.'}
              </p>
            </div>

            {/* License Section */}
            <div className="mt-4 sm:mt-6">
              <h2 className="text-base sm:text-lg font-medium mb-2">Surat Izin Praktik Psikolog</h2>
              <p className="text-xs sm:text-sm text-gray-700 break-words">
                {psychologist.sipp || 'Tidak tersedia'}
              </p>
            </div>

            {/* Video Section - Mobile: before testimonials, Desktop: hidden */}
            <div className="block lg:hidden mt-4 sm:mt-6">
              <div className="relative w-full rounded-lg overflow-hidden">
                {/* Aspect ratio container */}
                <div className="w-full relative pb-[60%] sm:pb-[56.25%]">
                  <div className="absolute inset-0">
                    <PsychologistVideoCarousel
                      video={psychologist.video}
                      youtubeVideos={psychologist.youtubeVideo}
                      profilePhoto={psychologist.profilePhoto}
                    />
                  </div>
                </div>
              </div>
              <div className="flex items-center justify-between mt-2">
                <span className="text-sm text-gray-600">5 Minute Mindfulness Meditation</span>
                <a href="#" className="text-main-500 text-sm font-medium">
                  Lihat semua
                </a>
              </div>
            </div>

            {/* Testimonials Section */}
            <div className="mt-4 sm:mt-6 mb-6">
              <h2 className="text-base sm:text-lg font-medium mb-2">
                Testimoni {psychologist.testimonyCount > 0 && `(${psychologist.testimonyCount})`}
              </h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
                {psychologist.testimonyCount > 0 ? (
                  [1, 2, 3].map((i) => (
                    <div key={i} className="p-2 sm:p-3 border border-gray-200 rounded-lg">
                      <div className="text-main-500 text-lg mb-1">
                        <QuoteIcon className="text-main-100" />
                      </div>
                      <p className="text-xs sm:text-sm text-gray-700 mb-1 sm:mb-2 line-clamp-3">
                        Konseling dengan {psychologist.nickname || psychologist.fullName.split(',')[0]} sangat
                        membantu dalam mengatasi masalah yang saya hadapi.
                      </p>
                      <p className="text-xs text-gray-600 mb-1 sm:mb-2 line-clamp-2">
                        Psikolog sangat profesional dan memberikan perspektif baru yang membantu saya melihat
                        masalah dari sudut pandang berbeda.
                      </p>
                      <a href="#" className="text-main-500 text-xs">
                        Baca selengkapnya
                      </a>
                      <div className="flex flex-wrap items-center mt-2 sm:mt-3 gap-2">
                        <p className="text-xs text-gray-700">Klien, {i % 2 === 0 ? 'Wanita' : 'Pria'}</p>
                        <div className="flex items-center">
                          <span className="text-yellow-400 mr-1">★</span>
                          <span className="text-xs">{Math.min(5, psychologist.overallRating || 5)}</span>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-xs sm:text-sm text-gray-500 col-span-full">Belum ada testimoni</p>
                )}
              </div>
              {psychologist.testimonyCount > 3 && (
                <button className="mt-2 sm:mt-3 text-main-500 text-xs sm:text-sm font-medium">
                  Lihat Lebih Banyak
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Bottom Bar */}
      <div className="fixed bottom-0 left-0 right-0 bg-white p-4 lg:hidden z-50 shadow-2xl">
        <Button
          onClick={() => setIsBookingModalOpen(true)}
          className="w-full bg-main-100 hover:bg-main-200 text-white py-3 rounded-lg font-semibold text-base"
        >
          Cek Jadwal
        </Button>
      </div>

      {/* Booking Modal for Mobile */}
      <BookingModal
        isOpen={isBookingModalOpen}
        onClose={() => setIsBookingModalOpen(false)}
        user={user}
        psychologist={psychologist}
        availabilityDate={availabilityDate}
      />
    </>
  )
}

function formatEducationYear(dateString: string): string {
  try {
    const graduationDate = new Date(dateString)
    if (isNaN(graduationDate.getTime())) return ''

    return graduationDate.getFullYear().toString()
  } catch (error) {
    return ''
  }
}
